{"name": "@alilc/lowcode-demo-next-pro", "version": "1.0.28", "description": "Low-Code Engine 低代码搭建引擎 Demo 项目 - 基于next实现的高级表单低代码物料", "repository": "**************:alibaba/lowcode-demo.git", "license": "MIT", "main": "index.js", "scripts": {"start": "build-scripts start --disable-reload --port 5556", "build": "build-scripts build", "prepublishOnly": "npm run build", "pub": "node ./scripts/watchdog.js && npm pub"}, "files": ["build"], "config": {}, "dependencies": {"@alilc/lowcode-datasource-fetch-handler": "^1.0.1", "@alilc/lowcode-plugin-code-editor": "^1.0.3", "@alilc/lowcode-plugin-code-generator": "^1.0.4", "@alilc/lowcode-plugin-components-pane": "^2.0.0", "@alilc/lowcode-plugin-datasource-pane": "^1.0.9", "@alilc/lowcode-plugin-inject": "^1.2.1", "@alilc/lowcode-plugin-manual": "^1.0.4", "@alilc/lowcode-plugin-schema": "^1.0.2", "@alilc/lowcode-plugin-simulator-select": "^1.0.2", "@alilc/lowcode-plugin-undo-redo": "^1.0.0", "@alilc/lowcode-plugin-zh-en": "^1.0.0", "@alilc/lowcode-plugin-set-ref-prop": "^1.0.1", "@alilc/lowcode-react-renderer": "^1.1.2", "@alilc/lowcode-setter-behavior": "^1.0.0", "@alilc/lowcode-setter-title": "^1.0.2", "antd": "^4.21.4", "moment": "^2.29.3", "uuid": "^8.3.2"}, "devDependencies": {"@alib/build-scripts": "^0.1.18", "@alilc/lowcode-engine": "^1.1.2", "@alilc/lowcode-engine-ext": "^1.0.0", "@alilc/lowcode-types": "^1.1.1", "@types/events": "^3.0.0", "@types/react": "^16.8.3", "@types/react-dom": "^16.8.2", "@types/streamsaver": "^2.0.0", "@types/uuid": "^8.3.4", "build-plugin-fusion": "^0.1.0", "build-plugin-moment-locales": "^0.1.0", "build-plugin-react-app": "^1.1.2", "fs-extra": "^10.0.1", "tsconfig-paths-webpack-plugin": "^3.2.0"}, "resolutions": {"@babel/core": "7.20.12"}}