{"name": "@alilc/lowcode-demo", "version": "1.3.19", "description": "Low-Code Engine 低代码搭建引擎 Demo 项目", "repository": "**************:alibaba/lowcode-demo.git", "license": "MIT", "main": "index.js", "scripts": {"start": "build-scripts start --disable-reload --port 5556", "build": "build-scripts build", "pub": "node ./scripts/watchdog.js && npm pub", "sync": "tnpm sync @alilc/lowcode-demo", "syncOss": "node ./scripts/sync-oss.js"}, "files": ["build"], "publishConfig": {"registry": "https://registry.npmjs.org/", "access": "public"}, "config": {}, "dependencies": {"@alilc/build-plugin-lce": "^0.0.4-beta.3", "@alilc/lce-graph-core": "^1.0.6", "@alilc/lce-graph-materials-pane": "^1.0.10", "@alilc/lce-graph-tools": "^1.0.6", "@alilc/lce-graph-x6-designer": "^1.0.6", "@alilc/lce-graph-x6-stencil": "^1.0.6", "@alilc/lowcode-datasource-fetch-handler": "^1.0.1", "@alilc/lowcode-plugin-code-editor": "^1.0.3", "@alilc/lowcode-plugin-code-generator": "^1.0.6", "@alilc/lowcode-plugin-components-pane": "^2.0.2", "@alilc/lowcode-plugin-datasource-pane": "^1.0.9", "@alilc/lowcode-plugin-inject": "^1.2.3", "@alilc/lowcode-plugin-manual": "^1.0.4", "@alilc/lowcode-plugin-resource-tabs": "^2.0.0", "@alilc/lowcode-plugin-schema": "^1.0.2", "@alilc/lowcode-plugin-set-ref-prop": "^1.0.1", "@alilc/lowcode-plugin-simulator-select": "^1.0.4", "@alilc/lowcode-plugin-undo-redo": "^1.0.0", "@alilc/lowcode-plugin-view-manager-pane": "^2.0.0", "@alilc/lowcode-plugin-zh-en": "^1.0.0", "@alilc/lowcode-react-renderer": "^1.1.2", "@alilc/lowcode-setter-behavior": "^1.0.0", "@alilc/lowcode-setter-title": "^1.0.2", "@formily/antd": "2.2.15", "@formily/core": "2.2.15", "@formily/next": "2.2.15", "@formily/react": "2.2.15", "@seada/antd-plugins": "^1.0.0-rc.27", "antd": "^4.21.4", "moment": "^2.29.3", "uuid": "^8.3.2"}, "devDependencies": {"@alib/build-scripts": "^0.1.18", "@alilc/lowcode-engine": "^1.1.2", "@alilc/lowcode-engine-ext": "^1.0.0", "@alilc/lowcode-types": "^1.1.1", "@types/events": "^3.0.0", "@types/react": "^16.8.3", "@types/react-dom": "^16.8.2", "@types/streamsaver": "^2.0.0", "@types/uuid": "^8.3.4", "build-plugin-fusion": "^0.1.0", "build-plugin-moment-locales": "^0.1.0", "build-plugin-react-app": "^1.1.2", "fs-extra": "^10.0.1", "tsconfig-paths-webpack-plugin": "^3.2.0"}, "resolutions": {"@babel/core": "7.20.12", "@formily/antd": "2.2.15", "@formily/core": "2.2.15", "@formily/react": "2.2.15", "@formily/next": "2.2.15", "@monaco-editor/react": "4.4.6"}}