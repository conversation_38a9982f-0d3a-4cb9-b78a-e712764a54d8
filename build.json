{"entry": {"demo-general/index": "./demo-general/src/index.ts", "demo-general/preview": "./demo-general/src/preview.tsx", "demo-basic-antd/index": "./demo-basic-antd/src/index.ts", "demo-basic-antd/preview": "./demo-basic-antd/src/preview.tsx", "demo-basic-fusion/index": "./demo-basic-fusion/src/index.ts", "demo-basic-fusion/preview": "./demo-basic-fusion/src/preview.tsx", "demo-next-pro/index": "./demo-next-pro/src/index.ts", "demo-next-pro/preview": "./demo-next-pro/src/preview.tsx", "demo-node-extended-actions/index": "./demo-node-extended-actions/src/index.ts", "demo-node-extended-actions/preview": "./demo-node-extended-actions/src/preview.tsx", "demo-lowcode-component/index": "./demo-lowcode-component/src/index.ts", "demo-lowcode-component/preview": "./demo-lowcode-component/src/preview.tsx", "demo-workspace/index": "./demo-workspace/src/index.ts", "demo-workspace/preview": "./demo-workspace/src/preview.tsx", "demo-graph-x6/index": "./demo-graph-x6/src/index.ts"}, "vendor": false, "devServer": {"hot": false}, "publicPath": "/", "externals": {"react": "var window.React", "react-dom": "var window.ReactDOM", "prop-types": "var window.PropTypes", "@alifd/next": "var window.Next", "@alilc/lowcode-engine": "var window.AliLowCodeEngine", "@alilc/lowcode-engine-ext": "var window.AliLowCodeEngineExt", "moment": "var window.moment", "lodash": "var window._"}, "plugins": [["build-plugin-react-app"], ["build-plugin-moment-locales", {"locales": ["zh-cn"]}], "./build.plugin.js"]}