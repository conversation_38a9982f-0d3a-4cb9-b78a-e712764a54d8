export function Logo() {
  return (
    <svg width="81px" height="81px" viewBox="0 0 81 81" version="1.1" xmlns="http://www.w3.org/2000/svg">
        <title>编组 2</title>
        <g id="初始状态" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" opacity="0.530575707">
            <g id="画板" transform="translate(-588.000000, -372.000000)" fill="#D3DAE3">
                <g id="编组-2" transform="translate(588.388365, 372.538369)">
                    <path d="M25.3304335,7.82094153 L25.3304335,31.7666377 C27.2283835,29.3039443 29.289407,27.4254328 31.5135039,26.1311031 C33.7376009,24.8367734 36.1690233,24.1008728 38.8077712,23.9234012 L70.8558558,23.9234012 L50.8385469,3.99345618 C47.9069733,1.47489232 44.400095,0.145500153 40.3179118,0.00527967345 C36.3899773,-0.0950426446 32.6516653,1.23434953 29.1029758,3.99345618 L25.3304335,7.82094153 Z" id="路径-2"></path>
                    <path d="M8.89593439,55.6015154 L8.89593439,79.9425831 C10.7938844,77.4798897 12.8549079,75.6013782 15.0790048,74.3070485 C17.3031018,73.0127188 19.7345242,72.2768182 22.3732722,72.0993466 L54.6655619,72.0993466 L34.3380382,51.7733084 C32.7844759,50.6534991 31.5711886,49.891734 30.6981763,49.488013 C28.5630972,48.5006544 26.1059193,47.9207718 24.490615,47.8837077 C23.2945098,47.8562623 20.7832074,47.8562623 18.1510026,48.7216532 C17.2449331,49.0195419 15.7059197,49.791848 13.5339623,51.0385713 L8.89593439,55.6015154 Z" id="路径-2" transform="translate(31.780748, 63.909310) rotate(180.000000) translate(-31.780748, -63.909310) "></path>
                    <path d="M41.8166175,39.946852 L41.8166175,63.9288937 C43.7145675,61.4662003 45.775591,59.5876888 47.9996879,58.2933591 C50.2237849,56.9990294 52.6377725,56.3856502 55.2416507,56.4532216 L86.8278179,56.4532216 L66.488288,36.1906641 C63.0898975,33.7791074 59.614075,32.573329 56.0608208,32.573329 C52.5075665,32.573329 49.1503412,33.6718259 45.9891448,35.8688195 L41.8166175,39.946852 Z" id="路径-2" transform="translate(64.322218, 48.251111) rotate(90.000000) translate(-64.322218, -48.251111) "></path>
                    <path d="M-6.56860415,23.5499066 L-6.56860415,47.5803247 C-4.70917687,45.1176314 -2.68998604,43.2391198 -0.511031668,41.9447902 C1.6679227,40.6504605 4.04999448,39.9145598 6.63518366,39.7370882 L37.9201642,39.7370882 L18.8954083,20.6769658 C15.9246181,17.8318526 12.3383866,16.3513366 8.13671404,16.2354177 C3.93504143,16.1194987 0.0835010089,17.5183863 -3.41790723,20.4320804 L-6.56860415,23.5499066 Z" id="路径-2" transform="translate(15.675780, 31.904545) rotate(270.000000) translate(-15.675780, -31.904545) "></path>
                </g>
            </g>
        </g>
    </svg>
  )
}

export function ShiftIcon() {
  return (
    <svg width="12px" height="12px" viewBox="0 0 12 12" version="1.1" xmlns="http://www.w3.org/2000/svg">
      <g id="初始状态" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="画板" transform="translate(-759.000000, -490.000000)" fill="#A5B0BF" fill-rule="nonzero">
          <g id="编组-2" transform="translate(616.000000, 351.000000)">
            <g id="编组" transform="translate(143.000000, 139.000000)">
              <rect id="矩形" opacity="0" x="0" y="0" width="12" height="12"></rect>
              <path d="M4.45071429,10.6607143 L7.54928571,10.6607143 C8.25257142,10.6607143 8.62928571,10.2488571 8.62928571,9.58585714 L8.62928571,7.33585714 L10.6733571,7.33585714 C11.0800714,7.33585714 11.4015,7.08471429 11.4015,6.70307143 C11.4015,6.46714287 11.2857857,6.29121429 11.1002143,6.11057143 L6.62035714,1.64571429 C6.42942858,1.44985714 6.2235,1.33928571 6.00257143,1.33928571 C5.7765,1.33928571 5.57571429,1.44985714 5.37964286,1.64571429 L0.9,6.11057143 C0.709285711,6.30128572 0.598714289,6.46714287 0.598714289,6.70307143 C0.598714289,7.08492858 0.920142855,7.33585714 1.332,7.33585714 L3.37092857,7.33585714 L3.37092857,9.58585714 C3.37092857,10.2488571 3.74764286,10.6607143 4.45092857,10.6607143 L4.45071429,10.6607143 Z M4.53107143,9.88221429 C4.34014287,9.88221429 4.20964287,9.7515 4.20964286,9.56592857 L4.20964286,6.74828571 C4.20964286,6.62271428 4.16442857,6.5775 4.04378571,6.5775 L1.72864286,6.5775 C1.69328571,6.5775 1.67335714,6.5625 1.67335714,6.53742857 C1.67335714,6.51728571 1.67828571,6.50228571 1.70335714,6.47721429 L5.89692857,2.30871429 C5.92224207,2.27622162 5.96138997,2.25756086 6.00257143,2.25833266 C6.03771429,2.25833266 6.06792858,2.26842858 6.108,2.30871429 L10.3015714,6.47721429 C10.3217143,6.50228572 10.3317857,6.51728572 10.3317857,6.53721429 C10.3317857,6.5625 10.3116429,6.5775 10.2765,6.5775 L7.95642857,6.5775 C7.83578571,6.5775 7.79078571,6.62271429 7.79078571,6.74828571 L7.79078571,9.56592857 C7.79078571,9.74657142 7.65514286,9.88221429 7.46935714,9.88221429 L4.53107143,9.88221429 Z" id="形状"></path>
            </g>
          </g>
        </g>
      </g>
    </svg>
  )
}

export function CommonIcon() {
  return (
    <svg width="12px" height="12px" viewBox="0 0 12 12" version="1.1" xmlns="http://www.w3.org/2000/svg">
      <g id="初始状态" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="画板" transform="translate(-781.000000, -490.000000)" fill="#A5B0BF" fill-rule="nonzero">
          <g id="编组-2" transform="translate(616.000000, 351.000000)">
            <g id="编组" transform="translate(165.000000, 139.000000)">
              <rect id="矩形" opacity="0" x="0" y="0" width="12" height="12"></rect>
              <path d="M9,1.00008 C9.55216,1.00008 10.02352,1.19536 10.41408,1.58592 C10.80464,1.97648 10.99992,2.44784 10.99992,3 C10.99992,3.55216 10.80464,4.02352 10.41408,4.41408 C10.02352,4.80464 9.55216,4.99992 9,4.99992 L7.99992,4.99992 L7.99992,6.99984 L9,6.99984 C9.55216,6.99984 10.02352,7.19512 10.41408,7.58568 C10.80464,7.97624 10.99992,8.4476 10.99992,8.99976 C10.99992,9.55192 10.80464,10.02328 10.41408,10.41384 C10.02352,10.8044 9.55216,10.99968 9,10.99968 C8.44784,10.99968 7.97648,10.8044 7.58592,10.41384 C7.19536,10.02328 7.00008,9.55192 7.00008,8.99976 L7.00008,7.99968 L5.00016,7.99968 L5.00016,8.99976 C5.00016,9.55192 4.80488,10.02328 4.41432,10.41384 C4.02376,10.8044 3.5524,10.99968 3.00024,10.99968 C2.44808,10.99968 1.97672,10.8044 1.58616,10.41384 C1.1956,10.02328 1.00032,9.55192 1.00032,8.99976 C1.00032,8.4476 1.1956,7.97624 1.58616,7.58568 C1.97672,7.19512 2.44808,6.99984 3.00024,6.99984 L4.00032,6.99984 L4.00032,4.99992 L3.00024,4.99992 C2.44808,4.99992 1.97672,4.80464 1.58616,4.41408 C1.1956,4.02352 1.00032,3.55216 1.00032,3 C1.00032,2.44784 1.1956,1.97648 1.58616,1.58592 C1.97672,1.19536 2.44808,1.00008 3.00024,1.00008 C3.5524,1.00008 4.02376,1.19536 4.41432,1.58592 C4.80488,1.97648 5.00016,2.44784 5.00016,3 L5.00016,4.00008 L7.00008,4.00008 L7.00008,3 C7.00008,2.44784 7.19536,1.97648 7.58592,1.58592 C7.97648,1.19536 8.44784,1.00008 9,1.00008 L9,1.00008 Z M4.00008,9 L4.00008,7.99992 L3,7.99992 C2.724,7.99992 2.48832,8.0976 2.29296,8.29296 C2.0976,8.48832 1.99992,8.724 1.99992,9 C1.99992,9.276 2.0976,9.51168 2.29296,9.70704 C2.48832,9.9024 2.724,10.00008 3,10.00008 C3.276,10.00008 3.51168,9.9024 3.70704,9.70704 C3.9024,9.51168 4.00008,9.276 4.00008,9 L4.00008,9 Z M3,1.99992 C2.724,1.99992 2.48832,2.0976 2.29296,2.29296 C2.0976,2.48832 1.99992,2.724 1.99992,3 C1.99992,3.276 2.0976,3.51168 2.29296,3.70704 C2.48832,3.9024 2.724,4.00008 3,4.00008 L4.00008,4.00008 L4.00008,3 C4.00008,2.724 3.9024,2.48832 3.70704,2.29296 C3.51168,2.0976 3.276,1.99992 3,1.99992 L3,1.99992 Z M7.00008,7.00008 L7.00008,5.00016 L5.00016,5.00016 L5.00016,7.00008 L7.00008,7.00008 Z M9,7.99992 L7.99992,7.99992 L7.99992,9 C7.99992,9.276 8.0976,9.51168 8.29296,9.70704 C8.48832,9.9024 8.724,10.00008 9,10.00008 C9.276,10.00008 9.51168,9.9024 9.70704,9.70704 C9.9024,9.51168 10.00008,9.276 10.00008,9 C10.00008,8.724 9.9024,8.48832 9.70704,8.29296 C9.51168,8.0976 9.276,7.99992 9,7.99992 Z M9,1.99992 C8.724,1.99992 8.48832,2.0976 8.29296,2.29296 C8.0976,2.48832 7.99992,2.724 7.99992,3 L7.99992,4.00008 L9,4.00008 C9.276,4.00008 9.51168,3.9024 9.70704,3.70704 C9.9024,3.51168 10.00008,3.276 10.00008,3 C10.00008,2.724 9.9024,2.48832 9.70704,2.29296 C9.51168,2.0976 9.276,1.99992 9,1.99992 Z" id="形状"></path>
            </g>
          </g>
        </g>
      </g>
    </svg>
  )
}

