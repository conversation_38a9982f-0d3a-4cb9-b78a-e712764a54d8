body {
  font-family: PingFangSC-Regular, Roboto, Helvetica Neue, Helvetica, Tahoma, Arial, PingFang SC-Light, Microsoft YaHei;
  font-size: 12px;
  * {
    box-sizing: border-box;
  }
}

body, #lce-container {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  top: 0;
  box-sizing: border-box;
  padding: 0;
  margin: 0;
  overflow: hidden;
  text-rendering: optimizeLegibility;
  -webkit-user-select: none;
  -webkit-user-drag: none;
  -webkit-text-size-adjust: none;
  -webkit-touch-callout: none;
  -webkit-font-smoothing: antialiased;
  #engine {
    width: 100%;
    height: 100%;
  }
}

html {
  min-width: 1024px;
}

.save-sample {
  width: 80px;
  height: 30px;
  background-color: #5584FF;
  border: none;
  outline: none;
  border-radius: 4px;
  color: white;
  cursor: pointer;
}

.load-assets {
  width: 100px;
  height: 30px;
  background-color: #5584FF;
  border: none;
  outline: none;
  border-radius: 4px;
  color: white;
  cursor: pointer;
}
