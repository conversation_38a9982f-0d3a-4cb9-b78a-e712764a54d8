import React from "react"

export const PageIcon = () => {
  return (
    <svg width="12px" height="12px" viewBox="0 0 12 12" version="1.1" xmlns="http://www.w3.org/2000/svg">
        <g id="视图面板分镜" stroke="none" stroke-width="1" fill="currentColor">
            <g id="画板" transform="translate(-633.000000, -208.000000)">
                <g id="编组-3" transform="translate(622.000000, 194.000000)">
                    <g id="编组" transform="translate(11.000000, 14.000000)">
                        <rect id="矩形" opacity="0" x="0" y="0" width="12" height="12"></rect>
                        <path d="M2.43333333,0 C1.63333334,0 0.999999996,0.633333328 0.999999996,1.4 L0.999999996,10.6 C0.999999996,11.3666667 1.63333334,12 2.43333333,12 L9.6,12 C10.3666667,12 11.0333333,11.3666667 11.0333333,10.6 L11.0333333,2.83333334 L8.13333333,0 L2.43333333,0 Z M9.2,11.1666667 L2.76666667,11.1666667 C2.23333334,11.1666667 1.9,10.8666667 1.9,10.3333333 L1.9,1.7 C1.9,1.16666667 2.23333332,0.9 2.76666667,0.9 L7.83333333,0.9 L7.83333333,2.43333333 C7.83333333,2.73333333 8.1,3 8.4,3 L10.1666667,3 L10.1666667,10.3333333 C10.2,10.8666667 9.76666668,11.1666667 9.2,11.1666667 Z M7.8,8.13333333 L4.2,8.13333333 C4,8.13333333 3.83333333,7.96666666 3.83333333,7.76666667 C3.83333333,7.56666667 4,7.4 4.2,7.4 L7.76666667,7.4 C7.96666668,7.4 8.13333333,7.56666667 8.13333333,7.76666667 C8.13333333,7.96666668 7.99999999,8.13333333 7.8,8.13333333 Z M7.8,6.73333334 L4.2,6.73333334 C4,6.73333334 3.83333333,6.56666667 3.83333333,6.36666667 C3.83333333,6.16666666 4,6 4.2,6 L7.76666667,6 C7.96666668,6 8.13333333,6.16666666 8.13333333,6.36666667 C8.13333333,6.56666667 7.99999999,6.73333334 7.8,6.73333334 Z M7.8,5.3 L4.2,5.3 C4,5.3 3.83333333,5.13333334 3.83333333,4.93333334 C3.83333333,4.73333333 4,4.56666667 4.2,4.56666667 L7.76666667,4.56666667 C7.96666668,4.56666667 8.13333333,4.73333333 8.13333333,4.93333334 C8.13333333,5.13333334 7.99999999,5.3 7.8,5.3 Z" id="形状"></path>
                    </g>
                </g>
            </g>
        </g>
    </svg>
  )
}